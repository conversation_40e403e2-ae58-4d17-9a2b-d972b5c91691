<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="warning" icon="el-icon-setting" size="mini" @click="openTimeConfigDialog">峰平谷时段配置</el-button>
          </el-form-item>
        </el-form>

        <!-- 峰平谷综合分析图表 -->
        <!-- 第一行：峰平谷统计卡片横向排列 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="8">
            <div class="stat-card peak-card">
              <div class="stat-header">
                <span class="stat-title">峰时段</span>
                <i class="el-icon-sunny stat-icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(totalStats.peak) }}</div>
                <div class="stat-unit">{{ unit }}</div>
                <div class="stat-percent">{{ calculatePercent('peak') }}%</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card flat-card">
              <div class="stat-header">
                <span class="stat-title">平时段</span>
                <i class="el-icon-partly-cloudy stat-icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(totalStats.flat) }}</div>
                <div class="stat-unit">{{ unit }}</div>
                <div class="stat-percent">{{ calculatePercent('flat') }}%</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card low-card">
              <div class="stat-header">
                <span class="stat-title">谷时段</span>
                <i class="el-icon-moon stat-icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(totalStats.low) }}</div>
                <div class="stat-unit">{{ unit }}</div>
                <div class="stat-percent">{{ calculatePercent('low') }}%</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 第二行：折线图和饼图 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="16">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">峰平谷趋势对比</span>
                <div class="chart-tools">
                  <el-tooltip content="切换图表类型" placement="top">
                    <el-dropdown @command="handleChartTypeChange" trigger="click">
                      <i class="el-icon-s-operation chart-tool-icon"></i>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="line">折线图</el-dropdown-item>
                        <el-dropdown-item command="bar">柱状图</el-dropdown-item>
                        <el-dropdown-item command="area">面积图</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download chart-tool-icon" @click="saveAsImage('mainChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="mainChart" class="main-chart-content" v-loading="loading"></div>
            </div>
          </el-col>
          <el-col :span="8">
            <!-- 峰平谷占比饼图 -->
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">峰平谷用电占比</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download chart-tool-icon" @click="saveAsImage('pieChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="pieChart" class="pie-chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

      </el-col>
    </el-row>

    <!-- 峰平谷时段配置组件 -->
    <SubTimeConfig
      v-model="timeConfigVisible"
      @save-success="handleTimeConfigSaveSuccess"
    />
  </div>
</template>

<script>

import * as echarts from 'echarts'
import {subAnalyse} from "@/api/biz/subAnalyse";
import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {meterTree} from "@/api/biz/meter";
import SubTimeConfig from "./subTimeConfig.vue";

export default {
  name: 'sub',
  components: {TimeAnalysisSelector, SubTimeConfig: SubTimeConfig},
  dicts: [ 'energy_type'],
  data() {
    return {
      // 加载状态
      loading: false,

      // 时间范围选择器
      dateRange: [],

      meterTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,

      // 查询参数
      queryParams: {
        meterIds: [],
        pointType: null,
        startTime: null,
        endTime: null
      },

      // 图表实例
      mainChart: null,
      pieChart: null,

      // 图表数据
      chartData: {
        peak: { times: [], energy: [] },
        flat: { times: [], energy: [] },
        low: { times: [], energy: [] }
      },

      // 图表类型
      chartType: 'line',

      // 统计数据
      totalStats: {
        peak: 0,
        flat: 0,
        low: 0,
        total: 0
      },

      // 单位配置
      unit: 'kWh',

      // 峰平谷时段配置相关
      timeConfigVisible: false

    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
  },
  mounted() {
    this.initCharts()
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 销毁图表实例，释放内存
    if (this.mainChart) {
      this.mainChart.dispose()
    }
    if (this.pieChart) {
      this.pieChart.dispose()
    }
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 获取区域表具树结构
    getMeterTree() {
      this.typeLoading = true
      meterTree({
        energyType: this.energyType
      }).then(res => {
        this.meterTreeOptions = res.data
        this.typeLoading = false
      })
    },
    getSubAnalyseData() {
      this.loading = true
      subAnalyse(this.queryParams).then(response => {
        console.log(response)
        if (response.code === 200 && response.data) {
          // 更新图表数据
          this.chartData = {
            peak: response.data.peak || { times: [], energy: [] },
            flat: response.data.flat || { times: [], energy: [] },
            low: response.data.low || { times: [], energy: [] }
          }
          // 计算统计数据
          this.calculateTotalStats()
          // 更新图表显示
          this.updateCharts()
        }
        this.loading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置其他参数
      this.queryParams.meterIds = []
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])

      // 清空表格数据
      this.tableData = []
      this.total = 0
      this.dateColumns = {}
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.queryParams.pageNum = 1
      this.getSubAnalyseData()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.startTime || !this.queryParams.endTime ) {
        this.$message.warning('请选择表具、时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "meter" 的节点 ID
      this.queryParams.meterIds = checkedNodes
        .filter(node => node.type === 'meter')
        .map(node => node.id);

      // 检查是否选择了表具
      if (this.queryParams.meterIds.length === 0) {
        this.$message.warning('请至少选择一个表具');
        return false;
      }
      return true;
    },
    // 时间范围选择器变化
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        // 初始化主图表
        this.mainChart = echarts.init(this.$refs.mainChart)
        // 初始化饼图
        this.pieChart = echarts.init(this.$refs.pieChart)

        // 设置初始配置
        this.updateCharts()
      })
    },

    // 更新图表数据
    updateCharts() {
      if (!this.mainChart || !this.pieChart) return

      // 更新主图表
      const mainOption = this.getMainChartOption()
      this.mainChart.setOption(mainOption)

      // 更新饼图
      const pieOption = this.getPieChartOption()
      this.pieChart.setOption(pieOption)
    },

    // 获取主图表配置
    getMainChartOption() {
      const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
      const seriesData = [
        {
          name: '峰时段',
          data: this.chartData.peak.energy,
          color: colors[0]
        },
        {
          name: '平时段',
          data: this.chartData.flat.energy,
          color: colors[1]
        },
        {
          name: '谷时段',
          data: this.chartData.low.energy,
          color: colors[2]
        }
      ]

      const series = seriesData.map((item, index) => {
        const baseConfig = {
          name: item.name,
          data: item.data,
          color: item.color,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            borderWidth: 2
          }
        }

        if (this.chartType === 'line') {
          return {
            ...baseConfig,
            type: 'line',
            areaStyle: {
              opacity: 0.1
            }
          }
        } else if (this.chartType === 'bar') {
          return {
            ...baseConfig,
            type: 'bar',
            barWidth: '60%'
          }
        } else if (this.chartType === 'area') {
          return {
            ...baseConfig,
            type: 'line',
            areaStyle: {
              opacity: 0.3
            },
            stack: 'total'
          }
        }
      })

      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold;margin-bottom:8px">${params[0].name}</div>`
            params.forEach(param => {
              result += `<div style="margin-bottom:4px">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: ${param.value ? param.value.toFixed(2) : 0} ${this.unit}
              </div>`
            })
            return result
          }
        },
        legend: {
          data: ['峰时段', '平时段', '谷时段'],
          top: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '8%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.peak.times,
          boundaryGap: this.chartType === 'bar',
          axisLabel: {
            formatter: this.formatAxisLabel,
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: `{value} ${this.unit}`
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              opacity: 0.5
            }
          }
        },
        series: series
      }
    },

    // 获取饼图配置
    getPieChartOption() {
      const data = [
        { value: this.totalStats.peak, name: '峰时段', itemStyle: { color: '#ff6b6b' } },
        { value: this.totalStats.flat, name: '平时段', itemStyle: { color: '#4ecdc4' } },
        { value: this.totalStats.low, name: '谷时段', itemStyle: { color: '#45b7d1' } }
      ].filter(item => item.value > 0)

      return {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} {d}%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          top: 'top',
          textStyle: {
            fontSize: 11
          }
        },
        series: [{
          name: '',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '55%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }]
      }
    },

    // 格式化坐标轴标签
    formatAxisLabel(value) {
      if (!value) return '';
      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return value;
        }
        if (value.includes('-') || value.includes('/')) {
          if (value.includes(':') || value.includes(' ')) {
            return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
          } else if (value.length <= 7) {
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          } else {
            return `${date.getMonth() + 1}/${date.getDate()}`;
          }
        } else if (value.length === 4) {
          return value;
        } else {
          return value;
        }
      } catch (e) {
        console.error('Date formatting error:', e);
        return value;
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.mainChart) {
        this.mainChart.resize()
      }
      if (this.pieChart) {
        this.pieChart.resize()
      }
    },

    // 保存图表为图片
    saveAsImage(chartRef) {
      let chart = null
      let chartName = ''

      switch(chartRef) {
        case 'mainChart':
          chart = this.mainChart
          chartName = '峰平谷趋势对比'
          break
        case 'pieChart':
          chart = this.pieChart
          chartName = '峰平谷用电占比'
          break
      }

      if (chart) {
        const url = chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        })

        const link = document.createElement('a')
        link.download = `${chartName}_${new Date().getTime()}.png`
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },

    // 切换图表类型
    handleChartTypeChange(command) {
      this.chartType = command
      this.updateCharts()
    },

    // 计算统计数据
    calculateTotalStats() {
      this.totalStats.peak = this.chartData.peak.energy.reduce((sum, val) => sum + (val || 0), 0)
      this.totalStats.flat = this.chartData.flat.energy.reduce((sum, val) => sum + (val || 0), 0)
      this.totalStats.low = this.chartData.low.energy.reduce((sum, val) => sum + (val || 0), 0)
      this.totalStats.total = this.totalStats.peak + this.totalStats.flat + this.totalStats.low
    },

    // 格式化数字显示
    formatNumber(value) {
      if (value === 0) return '0'
      if (value < 1000) return value.toFixed(1)
      if (value < 1000000) return (value / 1000).toFixed(1) + 'K'
      return (value / 1000000).toFixed(1) + 'M'
    },

    // 计算百分比
    calculatePercent(type) {
      if (this.totalStats.total === 0) return '0'
      const percent = (this.totalStats[type] / this.totalStats.total * 100)
      return percent.toFixed(1)
    },

    // 峰平谷时段配置相关方法
    // 打开时段配置对话框
    openTimeConfigDialog() {
      this.timeConfigVisible = true
    },

    // 时段配置保存成功回调
    handleTimeConfigSaveSuccess() {
      // 可以在这里添加保存成功后的处理逻辑
      // 比如重新加载图表数据等
      console.log('时段配置保存成功')
    },
  }
}

</script>


<style scoped>
.chart-container {
  background: #fff;
  padding: 24px;
  margin-top: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tool-icon {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
  margin-left: 10px;
}

.chart-tool-icon:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 180px;
  position: relative;
}

.main-chart-content {
  width: 100%;
  height: 450px;
  position: relative;
}

.pie-chart-content {
  width: 100%;
  height: 450px;
  position: relative;
}

/* 统计行样式 */
.stats-row {
  margin-top: 0;
  margin-bottom: 25px;
}

/* 图表行样式 */
.charts-row {
  margin-top: 0;
}

.stat-card {
  background: #fff;
  padding: 24px 28px;
  border-radius: 12px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);
  border-left: 5px solid;
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.peak-card {
  border-left-color: #ff6b6b;
}

.flat-card {
  border-left-color: #4ecdc4;
}

.low-card {
  border-left-color: #45b7d1;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  font-size: 16px;
  color: #606266;
  font-weight: 600;
}

.stat-icon {
  font-size: 24px;
  opacity: 0.7;
}

.peak-card .stat-icon {
  color: #ff6b6b;
}

.flat-card .stat-icon {
  color: #4ecdc4;
}

.low-card .stat-icon {
  color: #45b7d1;
}

.stat-content {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-right: 10px;
  line-height: 1.2;
}

.stat-unit {
  font-size: 14px;
  color: #909399;
  margin-right: 12px;
}

.stat-percent {
  font-size: 13px;
  color: #67C23A;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-content {
    height: 180px;
  }

  .main-chart-content {
    height: 350px;
  }

  .pie-chart-content {
    height: 350px;
  }

  .stat-card {
    padding: 20px 24px;
    height: 120px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-title {
    font-size: 15px;
  }

  .stat-icon {
    font-size: 22px;
  }

  .stats-row {
    margin-bottom: 20px;
  }

  .charts-row {
    margin-top: 0;
  }
}

@media (max-width: 576px) {
  .stats-row .el-col {
    margin-bottom: 15px;
  }

  .stat-card {
    height: 110px;
    padding: 18px 20px;
  }

  .stat-value {
    font-size: 22px;
  }

  .main-chart-content {
    height: 320px;
  }

  .pie-chart-content {
    height: 320px;
  }
}


</style>
