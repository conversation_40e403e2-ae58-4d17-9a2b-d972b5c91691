<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="请输入区域名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="areaOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--设备数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
          <el-form-item label="设备类型" prop="deviceTypeId">
            <el-select
              v-model="queryParams.deviceTypeId"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in deviceTypeQueryOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="设备名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入设备名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="设备编号" prop="number">
            <el-input
              v-model="queryParams.number"
              placeholder="请输入设备编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="在线状态" prop="onlineStatus">
            <el-select
              v-model="queryParams.onlineStatus"
              placeholder="在线状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.online_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['biz:device:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['biz:device:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['biz:device:remove']"
            >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['biz:device:export']"
            >导出
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="设备名称" align="center" prop="name"/>
          <el-table-column label="设备编号" align="center" prop="number"/>
          <el-table-column label="设备类型" align="center" prop="deviceTypeName"/>
          <el-table-column label="大类" align="center" prop="broadType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.device_broad_type" :value="scope.row.broadType"/>
            </template>
          </el-table-column>
          <el-table-column label="在线状态" align="center" prop="onlineStatus">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.online_status" :value="scope.row.onlineStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['biz:device:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['biz:device:remove']"
              >删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-setting"
                @click="handleControl(scope.row)"
              >控制
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-document"
                @click="handleOperationLog(scope.row)"
              >操作日志
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="大类" prop="broadType">
          <el-select
            v-model="form.broadType"
            placeholder="请选择"
            style="width: 100%"
            @change="broadTypeChange"
          >
            <el-option
              v-for="dict in dict.type.device_broad_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceTypeId">
          <el-select
            v-model="form.deviceTypeId"
            placeholder="设备类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in deviceTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称"/>
        </el-form-item>
        <el-form-item label="设备编号" prop="number">
          <el-input v-model="form.number" placeholder="请输入设备编号"/>
        </el-form-item>
        <el-form-item label="区域" prop="areaId">
          <el-cascader :options="areaOptions" v-model="form.areaId" :props="props"
                       filterable style="width: 100%"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 设备控制抽屉 -->
    <el-drawer
      title="设备控制"
      :visible.sync="controlDrawerVisible"
      direction="rtl"
      size="500px"
      :before-close="handleControlDrawerClose"
    >
      <el-scrollbar style="height: 100%;">
        <div style="padding: 20px;">
        <!-- 设备信息头部 -->
        <div v-if="currentControlDevice" class="device-info-header">
          <el-card shadow="never" style="margin-bottom: 20px;">
            <div class="device-info-content">
              <div class="device-main-info">
                <h3 class="device-name">
                  <i class="el-icon-cpu"></i>
                  {{ currentControlDevice.name }}
                </h3>
                <p class="device-number">设备编号: {{ currentControlDevice.number }}</p>
              </div>
              <div class="device-status">
                <el-tag
                  :type="currentControlDevice.onlineStatus === '1' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ currentControlDevice.onlineStatus === '1' ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <div v-if="controlLoading" style="text-align: center; padding: 50px;">
          <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
          <p>正在加载控制点位...</p>
        </div>

        <div v-else-if="controlPoints.length === 0" style="text-align: center; padding: 50px;">
          <el-empty description="该设备暂无可控制的点位"></el-empty>
        </div>

        <div v-else>
          <el-form ref="controlForm" :model="controlForm" label-width="100px">
            <div v-for="point in controlPoints" :key="point.pointId" class="control-point-item">
              <el-card shadow="hover" style="margin-bottom: 15px;">
                <div slot="header" class="clearfix">
                  <span style="font-weight: bold;">{{ point.pointName }}</span>
                  <span style="float: right;">
                    <el-tag
                      :type="point.clientAccess === 'RO' ? 'info' : 'success'"
                      size="mini"
                    >
                      {{ point.clientAccess === 'RO' ? '只读' : '可控制' }}
                    </el-tag>
                  </span>
                </div>

                <!-- 只读点位显示 -->
                <div v-if="point.clientAccess === 'RO'">
                  <!-- 数值类型只读显示 -->
                  <div v-if="point.valueType === '1'">
                    <el-form-item label="当前值">
                      <el-input
                        :value="(point.controlValue || '未设置') + (point.unit && point.controlValue !== null && point.controlValue !== undefined ? ' ' + point.unit : '')"
                        readonly
                        style="width: 100%;"
                      ></el-input>
                    </el-form-item>
                    <div class="point-info">
                      <span v-if="point.valueMin !== null && point.valueMin !== undefined && point.valueMax !== null && point.valueMax !== undefined">取值范围: {{ point.valueMin }} - {{ point.valueMax }}</span>
                      <span v-else>取值范围: 无限制</span>
                      <span v-if="point.unit" style="margin-left: 10px;">单位: {{ point.unit }}</span>
                    </div>
                  </div>

                  <!-- 选项类型只读显示 -->
                  <div v-else-if="point.valueType === '2'">
                    <el-form-item label="当前值">
                      <el-input
                        :value="(getOptionLabel(point.valueOption, point.controlValue) || '未设置') + (point.unit && point.controlValue !== null && point.controlValue !== undefined ? ' ' + point.unit : '')"
                        readonly
                        style="width: 100%;"
                      ></el-input>
                    </el-form-item>
                    <div class="point-info">
                      <span>可选项: {{ parseValueOptions(point.valueOption).map(opt => opt.name).join(' / ') }}</span>
                      <span v-if="point.unit" style="margin-left: 10px;">单位: {{ point.unit }}</span>
                    </div>
                  </div>

                  <div class="readonly-tip">
                    <el-alert
                      title="此点位为只读，无法进行控制操作"
                      type="info"
                      :closable="false"
                      show-icon
                    ></el-alert>
                  </div>
                </div>

                <!-- 可控制点位 -->
                <div v-else>
                  <!-- 数值类型控制 -->
                  <div v-if="point.valueType === '1'">
                    <!-- 当前值显示 -->
                    <el-form-item label="当前值">
                      <div class="current-value-display">
                        <el-tag
                          type="primary"
                          size="medium"
                          effect="plain"
                        >
                          <i class="el-icon-data-line tag-icon"></i>
                          <span>{{ point.controlValue !== null && point.controlValue !== undefined ? point.controlValue : '未设置' }}</span>
                          <span v-if="point.unit && point.controlValue !== null && point.controlValue !== undefined" style="margin-left: 5px;">{{ point.unit }}</span>
                        </el-tag>
                        <span class="value-unit" v-if="point.controlValue !== null && point.controlValue !== undefined">
                          <span v-if="point.valueMin !== null && point.valueMin !== undefined && point.valueMax !== null && point.valueMax !== undefined">(范围: {{ point.valueMin }} - {{ point.valueMax }})</span>
                          <span v-else>(范围: 无限制)</span>
                        </span>
                      </div>
                    </el-form-item>

                    <!-- 控制输入 -->
                    <el-form-item label="设置值">
                      <el-input-number
                        v-model="controlForm[point.pointId]"
                        :min="point.valueMin !== null && point.valueMin !== undefined ? point.valueMin : undefined"
                        :max="point.valueMax !== null && point.valueMax !== undefined ? point.valueMax : undefined"
                        :placeholder="point.valueMin !== null && point.valueMin !== undefined && point.valueMax !== null && point.valueMax !== undefined ? `范围: ${point.valueMin} - ${point.valueMax}` : '请输入数值'"
                        style="width: 100%;"
                      ></el-input-number>
                    </el-form-item>
                  </div>

                  <!-- 选项类型控制 -->
                  <div v-else-if="point.valueType === '2'">
                    <!-- 当前值显示 -->
                    <el-form-item label="当前值">
                      <div class="current-value-display">
                        <el-tag
                          type="primary"
                          size="medium"
                          effect="plain"
                        >
                          <i class="el-icon-check tag-icon"></i>
                          <span>{{ getOptionLabel(point.valueOption, point.controlValue) || '未设置' }}</span>
                          <span v-if="point.unit && point.controlValue !== null && point.controlValue !== undefined" style="margin-left: 5px;">{{ point.unit }}</span>
                        </el-tag>
                        <span class="value-options" v-if="parseValueOptions(point.valueOption).length > 0">
                          (可选: {{ parseValueOptions(point.valueOption).map(opt => opt.name).join(' / ') }})
                        </span>
                      </div>
                    </el-form-item>

                    <!-- 控制输入 -->
                    <el-form-item label="设置值">
                      <el-select
                        v-model="controlForm[point.pointId]"
                        placeholder="请选择控制值"
                        style="width: 100%;"
                      >
                        <el-option
                          v-for="option in parseValueOptions(point.valueOption)"
                          :key="option.value"
                          :label="option.name"
                          :value="option.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>

                  <!-- 控制按钮 -->
                  <div style="text-align: right; margin-top: 10px;">
                    <el-button
                      type="primary"
                      size="small"
                      @click="submitControl(point.pointId)"
                      :disabled="controlSubmitting[point.pointId] || (!controlForm[point.pointId] && controlForm[point.pointId] !== 0)"
                    >
                      {{ controlSubmitting[point.pointId] ? '执行中...' : '执行控制' }}
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </el-form>
        </div>
        </div>
      </el-scrollbar>
    </el-drawer>

    <!-- 操作日志弹出框 -->
    <el-dialog
      title="操作日志"
      :visible.sync="operationLogDialogVisible"
      width="60%"
      append-to-body
      :before-close="handleOperationLogDialogClose"
    >
      <div class="operation-log-container">
        <!-- 查询条件 -->
        <el-form :model="operationLogQueryParams" ref="operationLogQueryForm" size="small" :inline="true" label-width="80px">
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="operationLogDateRange"
              style="width: 300px"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="是否成功" prop="succeed">
            <el-select
              v-model="operationLogQueryParams.succeed"
              placeholder="请选择"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="dict in dict.type.yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleOperationLogQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetOperationLogQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作日志表格 -->
        <el-table v-loading="operationLogLoading" :data="operationLogList" style="margin-top: 20px;">
          <el-table-column label="点位名称" align="center" prop="pointName"/>
          <el-table-column label="操作值" align="center" prop="value"/>
          <el-table-column label="操作前值" align="center" prop="beforeValue"/>
          <el-table-column label="操作后值" align="center" prop="afterValue"/>
          <el-table-column label="是否成功" align="center" prop="succeed">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.yes_no" :value="scope.row.succeed"/>
            </template>
          </el-table-column>
          <el-table-column label="操作时间" align="center" prop="createTime" width="180"/>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="operationLogTotal > 0"
          :total="operationLogTotal"
          :page.sync="operationLogQueryParams.pageNum"
          :limit.sync="operationLogQueryParams.pageSize"
          @pagination="getOperationLogList"
          style="margin-top: 20px;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listDevice, getDevice, delDevice, addDevice, updateDevice} from "@/api/biz/device";
import {deviceTypeOption} from "@/api/biz/deviceType";
import {listArea} from "@/api/biz/area";
import {control, controlPoint} from "@/api/biz/devicePoint";
import {listOperationLog} from "@/api/biz/operationLog";

export default {
  name: "Device",
  dicts: ['online_status', 'device_broad_type', 'yes_no'],
  data() {
    return {
      // 设备控制相关
      controlDrawerVisible: false,
      controlLoading: false,
      controlPoints: [],
      controlForm: {},
      controlSubmitting: {},
      currentControlDevice: null,

      // 操作日志相关
      operationLogDialogVisible: false,
      operationLogLoading: false,
      operationLogList: [],
      operationLogTotal: 0,
      operationLogDateRange: [],
      currentLogDevice: null,
      operationLogQueryParams: {
        pageNum: 1,
        pageSize: 10,
        objId: undefined,
        objName: undefined,
        succeed: undefined,
        startTime: undefined,
        endTime: undefined
      },

      // 区域名称
      areaName: undefined,
      areaOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      props: {
        emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },

      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 类型选项
      deviceTypeOptions: [],
      // 查询选项
      deviceTypeQueryOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceTypeId: undefined,
        broadType: undefined,
        name: undefined,
        number: undefined,
        onlineStatus: undefined,
        areaId: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        broadType: [
          {required: true, message: "设备大类不能为空", trigger: "blur"}
        ],
        deviceTypeId: [
          {required: true, message: "设备类型不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "设备名称不能为空", trigger: "blur"}
        ],
        number: [
          {required: true, message: "设备编号不能为空", trigger: "blur"}
        ],
        areaId: [
          {required: true, message: '区域不能为空', trigger: 'blur'}
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceTypeOption();
    this.getAreaTree();
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    // 操作日志按钮事件
    handleOperationLog(row) {
      this.currentLogDevice = row;
      this.operationLogDialogVisible = true;
      this.operationLogQueryParams.objId = row.id;
      this.operationLogQueryParams.objName = row.name;
      this.operationLogQueryParams.pageNum = 1;
      this.operationLogDateRange = [];
      this.operationLogQueryParams.startTime = undefined;
      this.operationLogQueryParams.endTime = undefined;
      this.getOperationLogList();
    },

    // 获取操作日志列表
    getOperationLogList() {
      this.operationLogLoading = true;

      // 处理时间范围
      if (this.operationLogDateRange && this.operationLogDateRange.length === 2) {
        this.operationLogQueryParams.startTime = this.operationLogDateRange[0];
        this.operationLogQueryParams.endTime = this.operationLogDateRange[1];
      } else {
        this.operationLogQueryParams.startTime = undefined;
        this.operationLogQueryParams.endTime = undefined;
      }

      listOperationLog(this.operationLogQueryParams).then(response => {
        this.operationLogList = response.rows || [];
        this.operationLogTotal = response.total || 0;
      }).catch(error => {
        this.$modal.msgError('获取操作日志失败: ' + (error.message || '未知错误'));
        this.operationLogList = [];
        this.operationLogTotal = 0;
      }).finally(() => {
        this.operationLogLoading = false;
      });
    },

    // 操作日志查询
    handleOperationLogQuery() {
      this.operationLogQueryParams.pageNum = 1;
      this.getOperationLogList();
    },

    // 重置操作日志查询
    resetOperationLogQuery() {
      this.operationLogDateRange = [];
      this.operationLogQueryParams.succeed = undefined;
      this.operationLogQueryParams.startTime = undefined;
      this.operationLogQueryParams.endTime = undefined;
      this.operationLogQueryParams.pageNum = 1;
      this.getOperationLogList();
    },

    // 关闭操作日志弹出框
    handleOperationLogDialogClose() {
      this.operationLogDialogVisible = false;
      this.operationLogList = [];
      this.operationLogTotal = 0;
      this.operationLogDateRange = [];
      this.currentLogDevice = null;
      this.operationLogQueryParams = {
        pageNum: 1,
        pageSize: 10,
        objId: undefined,
        objName: undefined,
        succeed: undefined,
        startTime: undefined,
        endTime: undefined
      };
    },

    // 控制按钮事件
    handleControl(row) {
      this.currentControlDevice = row;
      this.controlDrawerVisible = true;
      this.controlLoading = true;
      this.controlForm = {};
      this.controlSubmitting = {};

      // 获取设备控制点位
      controlPoint({
        deviceId: row.id
      }).then(res => {
        this.controlPoints = res.data || [];
        // 初始化控制表单和提交状态
        this.controlPoints.forEach(point => {
          this.$set(this.controlForm, point.pointId, point.controlValue);
          this.$set(this.controlSubmitting, point.pointId, false);
        });
      }).catch(error => {
        this.$modal.msgError('获取控制点位失败: ' + (error.message || '未知错误'));
        this.controlPoints = [];
      }).finally(() => {
        this.controlLoading = false;
      });
    },

    // 关闭控制抽屉
    handleControlDrawerClose() {
      this.controlDrawerVisible = false;
      this.controlPoints = [];
      this.controlForm = {};
      this.controlSubmitting = {};
      this.currentControlDevice = null;
    },

    // 解析选项值
    parseValueOptions(valueOption) {
      if (!valueOption) return [];
      try {
        return JSON.parse(valueOption);
      } catch (e) {
        console.error('解析选项值失败:', e);
        return [];
      }
    },

    // 获取选项标签
    getOptionLabel(valueOption, value) {
      if (!valueOption || value === null || value === undefined) return '';
      try {
        const options = JSON.parse(valueOption);
        const option = options.find(opt => opt.value === value || opt.value === String(value));
        return option ? option.name : value;
      } catch (e) {
        return value;
      }
    },

    // 提交控制指令
    submitControl(pointId) {
      const controlValue = this.controlForm[pointId];

      if (controlValue === null || controlValue === undefined || controlValue === '') {
        this.$modal.msgWarning('请设置控制值');
        return;
      }

      // 设置提交状态
      this.$set(this.controlSubmitting, pointId, true);

      // 执行控制
      control({
        deviceId: this.currentControlDevice.id,
        pointId: pointId,
        controlValue: controlValue
      }).then(res => {
        this.$modal.msgSuccess('控制指令发送成功');

        // 更新当前控制值显示
        const point = this.controlPoints.find(p => p.pointId === pointId);
        if (point) {
          point.controlValue = controlValue;
        }
      }).catch(error => {
        this.$modal.msgError('控制指令发送失败: ' + (error.message || '未知错误'));
      }).finally(() => {
        // 确保重置加载状态
        this.$set(this.controlSubmitting, pointId, false);
        // 添加延时确保状态更新
        this.$nextTick(() => {
          this.controlSubmitting[pointId] = false;
        });
      });
    },
    /** 查询区域下拉树结构 */
    getAreaTree() {
      listArea().then(response => {
        this.areaOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.areaId = data.id
      this.handleQuery()
    },

    broadTypeChange() {
      this.form.deviceTypeId = undefined
      this.getTypeByBroadType();
    },
    // 设备类型查询选项
    getDeviceTypeOption() {
      deviceTypeOption().then(res => {
        this.deviceTypeQueryOptions = res.data;
      })
    },
    // 设备类型选项
    getTypeByBroadType() {
      deviceTypeOption({
        broadType: this.form.broadType,
      }).then(res => {
        this.deviceTypeOptions = res.data;
      })
    },
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceTypeId: undefined,
        broadType: undefined,
        name: undefined,
        number: undefined,
        onlineStatus: undefined,
        projectId: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.areaId = undefined
      this.$refs.tree.setCurrentKey(null)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getDevice(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.getTypeByBroadType();
        this.open = true;
        this.title = "修改设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId;
          if (this.form.id != null) {
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true;
        return delDevice(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.control-point-item {
  margin-bottom: 15px;
}

.point-info {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.point-info span {
  display: inline-block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 加载状态样式 */
.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 只读点位样式 */
.readonly-tip {
  margin-top: 10px;
}

.readonly-tip .el-alert {
  padding: 8px 12px;
}

.readonly-tip .el-alert__title {
  font-size: 12px;
}

/* 只读输入框样式 */
.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}

/* 只读输入框聚焦时不显示边框变化 */
.el-input.is-disabled .el-input__inner:focus {
  border-color: #e4e7ed;
}

/* 当前值显示样式 */
.current-value-display {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.current-value-display .el-tag {
  font-weight: bold;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.value-unit,
.value-options {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
}



/* 标签内图标样式 */
.tag-icon {
  margin-right: 6px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
}

/* 设备信息头部样式 */
.device-info-header .el-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.device-info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-main-info {
  flex: 1;
}

.device-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  display: flex;
  align-items: center;
}

.device-name i {
  margin-right: 8px;
  font-size: 20px;
  color: #409eff;
}

.device-number {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.device-status {
  display: flex;
  align-items: center;
}

/* 自定义滚动条样式 */
.el-scrollbar__bar.is-vertical > div {
  background-color: #c1c1c1;
  border-radius: 4px;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.el-scrollbar__bar.is-vertical > div:hover {
  opacity: 1;
}

/* 操作日志弹出框样式 */
.operation-log-container {
  padding: 0;
}

.operation-log-container .el-form {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 0;
}

.operation-log-container .el-table {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.operation-log-container .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.operation-log-container .el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.operation-log-container .el-table tr:hover td {
  background-color: #f5f7fa;
}

/* 操作日志表格内容样式 */
.operation-log-container .el-table .cell {
  padding: 8px 12px;
  line-height: 1.4;
}

/* 分页样式 */
.operation-log-container .pagination-container {
  text-align: center;
  padding: 20px 0;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
}

</style>
