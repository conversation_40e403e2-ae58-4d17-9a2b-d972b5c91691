<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div>
          <el-input
            v-model="deviceSearchName"
            placeholder="输入关键字以查找设备"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div>
          <el-scrollbar class="tree-scrollbar">
            <el-tree
              :data="deviceTreeOptions"
              :props="deviceTreeProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <!--操作日志数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
          <el-form-item label="点位名称" prop="pointName">
        <el-input
          v-model="queryParams.pointName"
          placeholder="请输入点位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否成功" prop="succeed">
        <el-select
          v-model="queryParams.succeed"
          placeholder="请选择"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="dict in dict.type.yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 300px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:operationLog:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:operationLog:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="operationLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="操作对象名称" align="center" prop="objName"/>
      <el-table-column label="点位名称" align="center" prop="pointName"/>
      <el-table-column label="操作值" align="center" prop="value"/>
      <el-table-column label="操作前值" align="center" prop="beforeValue"/>
      <el-table-column label="操作后值" align="center" prop="afterValue"/>
      <el-table-column label="是否成功" align="center" prop="succeed">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.yes_no" :value="scope.row.succeed"/>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center" prop="createTime"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:operationLog:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {listOperationLog, delOperationLog} from "@/api/biz/operationLog";
import {deviceTree} from "@/api/biz/device";

export default {
  name: "OperationLog",
  dicts: ['yes_no'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作日志表格数据
      operationLogList: [],
      // 时间范围
      dateRange: [],
      // 设备搜索名称
      deviceSearchName: undefined,
      // 设备树选项
      deviceTreeOptions: [],
      deviceTreeProps: {
        children: 'children',
        label: 'name'
      },
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        objId: undefined,
        pointName: undefined,
        value: undefined,
        beforeValue: undefined,
        afterValue: undefined,
        valueType: undefined,
        succeed: undefined,
        startTime: undefined,
        endTime: undefined,
        deviceId: undefined,
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceTree();
  },
  watch: {
    // 根据名称筛选设备树
    deviceSearchName(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    /** 查询操作日志列表 */
    getList() {
      this.loading = true;

      // 处理时间范围参数
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = undefined;
        this.queryParams.endTime = undefined;
      }

      listOperationLog(this.queryParams).then(response => {
        this.operationLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.deviceId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除操作日志编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delOperationLog(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理时间范围参数
      const params = { ...this.queryParams };
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }

      this.download('biz/operationLog/export', params, `operationLog_${new Date().getTime()}.xlsx`)
    },

    /** 查询设备树结构 */
    getDeviceTree() {
      deviceTree({
        projectId: this.projectId
      }).then(response => {
        this.deviceTreeOptions = response.data || []
      }).catch(error => {
        console.error('获取设备树失败:', error)
        this.deviceTreeOptions = []
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      // 只有当点击的是设备节点时才进行筛选
      if (data.type === 'device') {
        this.queryParams.deviceId = data.id
        this.queryParams.objId = data.id
        this.handleQuery()
      }
    }
  }
};
</script>

<style scoped>

.tree-scrollbar {
  height: 700px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.el-tree {
  background-color: transparent;
  padding: 10px;
}

.el-tree-node__content {
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  margin: 2px 0;
}

.el-tree-node__content:hover {
  background-color: #f0f9ff;
}

.el-tree-node.is-current > .el-tree-node__content {
  background-color: #409eff;
  color: white;
}

.el-tree-node.is-current > .el-tree-node__content:hover {
  background-color: #337ecc;
}
</style>
